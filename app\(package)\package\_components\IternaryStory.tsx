import Image from "next/image";
import { Dot, MapPin, MoveRight } from "lucide-react";
import { Destination } from "@/app/types/package";
import Link from "next/link";
import { useRouter } from "next/router";
import { Activity } from "@/app/types/pack";

export default function IternaryStory({
  destinations,
  activity,
}: {
  destinations: Destination[];
  activity: Activity[];
}) {
  const location =
    typeof window !== "undefined" ? window.location.pathname : "";

  return (
    <div className="lg:w-1/2">
      <h1
        className="relative text-transparent mt-12 bg-clip-text font-Poppins text-[20px] lg:text-[30px] not-italic font-semibold leading-normal tracking-[0.18px]"
        style={{
          textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.24)",
          backgroundImage:
            "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
        }}
      >
        Day wise Itinerary
      </h1>
      <h1 className="text-xs lg:text-base text-neutral-600 mt-1">
        Planned Activities by Day
      </h1>
      <div className="mt-0">
        <div
          className="flex flex-col mt-8 w-full p-3 lg:w-4/5 mx-auto lg:mx-0 rounded-[14px] bg-white border-[1.5px] border-[#FF7865]"
          style={{ boxShadow: "2px 4px 19.9px 0px rgba(255, 120, 101, 0.20)" }}
        >
          <h1
            className="text-transparent bg-clip-text flex justify-center font-Poppins mt-3 text-[18px] lg:text-[22px] not-italic font-semibold leading-normal tracking-[0.18px]"
            style={{
              textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.24)",
              backgroundImage:
                "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
            }}
          >
            Activities
          </h1>
          {activity.slice(0, 1).map((a) => (
            <div key={a._id}>
              <h1 className="text-[#29b687] text-[16px] lg:text-[20px] mt-2 font-semibold pl-4 lg:pl-0">
                {" "}
                Day {a.day}
              </h1>
              {a.event.slice(0, 3).map((ae, index) => (
                <div key={ae._id}>
                  <div className="pl-8 lg:pl-0 grid grid-cols-2 gap-2 lg:gap-4 mt-4">
                    <span className="text-xs lg:text-base text-left text-[#FF7865] font-semibold whitespace-pre-wrap">
                      {ae.timePeriod.toUpperCase()}
                    </span>
                    <span className="text-[14px] lg:text-[15px] text-left mr-6">
                      {ae.name}
                    </span>
                  </div>
                  {index < a.event.slice(0, 3).length - 1 && (
                    <div className="px-2 lg:px-4">
                      <hr className="mt-2 mb-2 w-full mx-auto border-gray-300" />
                    </div>
                  )}
                </div>
              ))}

              {a.event.length > 3 && (
                <div className="pl-5 lg:pl-6 flex flex-col flex-wrap gap-1 lg:gap-2">
                  <span className="text-xs lg:text-base text-[#FF7865] font-semibold mb-[-10px]">
                    .
                  </span>
                  <span className="text-xs lg:text-base text-[#FF7865] font-semibold mb-[-10px]">
                    .
                  </span>
                  <span className="text-xs lg:text-base text-[#FF7865] font-semibold mb-[-10px]">
                    .
                  </span>
                  <span className="text-xs lg:text-base text-[#FF7865] font-semibold mb-[-10px]">
                    .
                  </span>
                </div>
              )}
            </div>
          ))}
          <div className="flex w-full p-1 lg:p-2 text-white justify-center items-center mt-4">
            <Link
              href={`${location}/activities`}
              className="text-[16px] lg:text-[20px] py-2 lg:py-3 w-full flex justify-center rounded-lg font-medium bg-[#31DAA1] text-white"
            >
              View Itinerary
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
