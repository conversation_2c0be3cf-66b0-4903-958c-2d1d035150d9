"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import { Building, Utensils, CarFront, IndianRupee } from "lucide-react";
import { FaIndianRupeeSign } from "react-icons/fa6";
import Link from "next/link";

const Wishlist = () => {
  const wishlist = [
    {
      label: "Enchanting Ooty",
      category: "Silver",
    },
    {
      label: "Enchanting Manali 6 Day Himalayan",
      category: "Silver",
    },
  ];

  return (
    <div className="  flex flex-col ">
      {wishlist.map((data, index) => (
        <div
          key={index}
          className="w-[312px] h-[112px] rounded-[14px] border-2 mb-[20px] shadow-pkgShadow bg-white flex "
        >
          <div className="m-[6px] h-[96px] w-[128px] rounded-[8px] shadow-pkg-imgShadow bg-gray-200">
            <p className="flex font-Poppins justify-center items-center w-[49px] h-[19px] px-[2px] rounded-8/2 shadow-goldShadow text-white text-[9px] font-semibold tracking-[1px] bg-silverGradient ">
              {data.category}
            </p>
          </div>

          <div>
            <div className="ml-[4px] mt-[8px] h-[34px] flex mr-3">
              <h2 className="w-[129px] text-[#1EC089] h- font-semibold non-italic text-[11px]">
                {data.label}
              </h2>
              <p className="w-[37px] h-[16px]  rounded-[7px] bg-[#1EC089] flex items-center justify-center text-white text-[7px] font-semibold leading-normal font-inter">
                2N / 3D
              </p>
            </div>
            <div className=" ml-[4px] mt-[4px] flex">
              <p className="text-[#FF7865] text-[7px] font-semibold leading-normal tracking-[0.07px]">
                Manali - 2N
              </p>
              <p className="pl-[15px] text-[#FF7865] text-[7px] font-semibold leading-normal tracking-[0.07px]">
                Shiml - 3D
              </p>
            </div>
            <div className="flex justify-between items-center">
              <div className=" ml-[8px] mt-[14px] flex justify-between w-[85px] h-[28.5] ">
                <div className="w-[21px] h-[22.5px] flex flex-col justify-between items-center">
                  <Building className="h-[9.6px] w-[12px] text-[#8391A1]" />
                  <p className="text-[#8391A1]  h-[8px] text-[5px] font-semibold tracking-[0.05px]">
                    2 Hotels
                  </p>
                </div>
                <div className="w-[21px] h-[22.5px] flex flex-col justify-between items-center">
                  <Utensils className="h-[9.6px] w-[12px] text-[#8391A1]" />
                  <p className="text-[#8391A1]  h-[8px] text-[5px] font-semibold tracking-[0.05px]">
                    2 Meals
                  </p>
                </div>
                <div className="w-[21px] h-[22.5px] flex flex-col justify-between items-center">
                  <CarFront className="h-[9.6px] w-[12px] text-[#8391A1]" />
                  <p className="text-[#8391A1]  h-[8px] text-[5px] font-semibold tracking-[0.05px]">
                    2 Cab
                  </p>
                </div>
              </div>
              <div className=" mt-[12px] relative h-[31px] ">
                <div className="flex items-center  mr-[5px]">
                  <FaIndianRupeeSign
                    className="text-[#FF7865] text-semibold "
                    size={15}
                  />
                  <p className="text-[#FF7865] text-[16px] mr-[5px] font-semibold leading-[21.6px] tracking-[0.48px] font-montserrat ">
                    8,999
                  </p>
                </div>
                <p className="absolute right-[11px] bottom-[4px]  text-[#FF7865] text-[6px] font-medium tracking-[0.06px] leading-0">
                  per person
                </p>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Wishlist;
