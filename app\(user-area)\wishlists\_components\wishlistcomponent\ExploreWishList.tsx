"use client";
import React, { useState } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import Wishlist from "./Wishlist";
import MyIternary from "./MyIternary";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

const ExploreWishList = () => {
  const router = useRouter();

  const clickBack = () => {
    router.push("/");
  };

  return (
    <div className="mb-[900px] h-auto">
       <div
        className="fixed lg:hidden block top-0 text-center flex items-center w-full h-[100px] bg-white z-10"
        style={{ boxShadow: "0px 4px 36.1px 0px rgba(190, 190, 190, 0.22)" }}
      >
        <span className="pl-[40px] flex items-center">
          <button onClick={clickBack}>
            <ArrowLeft className="h-[33px] w-[33px] text-[#FF5F5F]" />
          </button>
        </span>
        <h1
          className="text-center ml-[16px] font-Poppins text-[18px] not-italic font-semibold leading-normal tracking-[0.18px]"
          style={{
            textShadow: "2px 4px 14.3px rgba(255, 120, 101, 0.20)",
            backgroundImage:
              "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
            backgroundClip: "text",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          Wishlist
        </h1>
      </div>
     
      <div className="pt-[120px]  flex justify-center  ">
        <Tabs defaultValue="myIternary" className="w-[300px] h-[47px]  ">
          <div
            className="flex items-center rounded-[63px]"
            style={{ background: "rgba(197, 197, 197, 0.15)" }}
          >
            <TabsList>
              <TabsTrigger value="myIternary">
                <button>My Iternary</button>
              </TabsTrigger>
              <TabsTrigger value="wishlist">
                <button>Wishlist</button>
              </TabsTrigger>
            </TabsList>
          </div>
          <div className=" mt-[41px]   mb-[1000px] h-auto ">
            <TabsContent value="myIternary" className="">
              <MyIternary />
            </TabsContent>
            <TabsContent value="wishlist" className="">
              <Wishlist />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default ExploreWishList;
