"use client";
import {
  Building,
  CarFront,
  IndianRupee,
  MapPin,
  Clock,
  Users,
  Plane,
  Utensils,
  Heart,
} from "lucide-react";
import Image from "next/image";
import { PackageType } from "@/app/types/package";
import { NEXT_PUBLIC_IMAGE_URL } from "@/app/utils/constants/apiUrls";
import { cn, formatIndianNumber } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import { HotelMeal, Vehicle } from "@/app/types/pack";

// Helper function to get meal plan description
const getMealPlanDescription = (code: string) => {
  if (typeof code !== "string") {
    return "N/A";
  }
  switch (code.toUpperCase()) {
    case "EP":
      return "Room Only";
    case "CP":
      return "Breakfast Included";
    case "MAP":
      return "Breakfast and Dinner Included";
    case "AP":
      return "All Meals Included";
    default:
      return code;
  }
};

const FilterCardList = (props: { package: PackageType }) => {
  const router = useRouter();
  console.log("Vehicle data:", props.package.vehicle);
  const {
    _id,
    packageImg,
    packageName,
    planName,
    destination,
    noOfDays,
    noOfNight,
    vehicle,
    hotel,
    hotelCount,
    perPerson,
    hotelMeal,
  } = props.package;

  const [isWishlisted, setIsWishlisted] = useState(false);

  const uniqueMealPlans = useMemo(() => {
    if (!hotelMeal || hotelMeal.length === 0) {
      return [];
    }

    const mealPlanCounts = hotelMeal.reduce((acc, meal) => {
      const description = getMealPlanDescription(meal.mealPlan);
      if (description !== "N/A") {
        acc[description] = (acc[description] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const plans = Object.keys(mealPlanCounts);

    if (
      plans.includes("Breakfast Included") &&
      plans.includes("Breakfast and Dinner Included")
    ) {
      if (
        mealPlanCounts["Breakfast and Dinner Included"] >=
        mealPlanCounts["Breakfast Included"]
      ) {
        return plans.filter((plan) => plan !== "Breakfast Included");
      } else {
        return plans.filter((plan) => plan !== "Breakfast and Dinner Included");
      }
    }

    return plans;
  }, [hotelMeal]);

  const mealPlanDisplay =
    uniqueMealPlans.length > 1
      ? uniqueMealPlans.filter((plan) => plan !== "N/A").join(", ")
      : uniqueMealPlans.join(", ");

  const amenities = useMemo(() => {
    const allAmenities = new Set<string>();
    hotel?.forEach((h) => h.amenities.forEach((a) => allAmenities.add(a)));
    return Array.from(allAmenities)
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);
  }, [hotel]);

  const handleWishlistClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    // Add wishlist logic here
  };

  return (
    <div
      onClick={() => router.push(`/package/${_id}`)}
      className={cn(
        "bg-white rounded-3xl shadow-lg overflow-hidden cursor-pointer transition-transform duration-300 w-full max-w-sm mx-auto flex flex-col hover:shadow-2xl hover:-translate-y-2 mb-8 border-2",
        props.package.planName === "Gold"
          ? "border-[#EF831E]/60 hover:border-[#EF831E] hover:shadow-[0_0_5px_rgba(239,131,30,0.8)]"
          : props.package.planName === "Silver"
          ? "border-[#95A1AF]/60 hover:border-[#95A1AF] hover:shadow-[0_0_5px_rgba(149,161,175,0.8)]"
          : props.package.planName === "Platinum"
          ? "border-[#CA0B0B]/60 hover:border-[#CA0B0B] hover:shadow-[0_0_5px_rgba(202,11,11,0.8)]"
          : "border-green-500/60 hover:border-green-500"
      )}
    >
      <div className="relative w-full h-48">
        <Image
          src={NEXT_PUBLIC_IMAGE_URL + packageImg[0]}
          alt={packageName}
          fill
          className="object-cover"
        />
        <div
          className={cn(
            "absolute top-0 left-0 flex justify-center shadow-goldShadow px-3 py-1 ml-3 mt-3 text-xs bg-goldGradient text-white rounded-lg",
            props.package.planName === "Gold"
              ? "bg-goldGradient"
              : props.package.planName === "Silver"
              ? "bg-silverGradient"
              : props.package.planName === "Platinum"
              ? "bg-platinumGradient"
              : ""
          )}
        >
          {planName}
        </div>
      {/* <button
          onClick={handleWishlistClick}
          className="absolute top-3 right-3 bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors"
        >
          <Heart
            size={20}
            className={`transition-colors ${
              isWishlisted ? "text-red-500 fill-current" : "text-gray-500"
            }`}
          />
        </button>*/}
      </div>
      <div className="p-4 flex flex-col flex-grow">
        <h3 className="text-[#1EC089] text-lg font-semibold truncate-2 drop-shadow-sm">
          {packageName}
        </h3>
        <div className="flex items-center text-gray-600 mt-2">
          <MapPin size={16} className="mr-2 text-green-500" />
          <span className="text-sm">
              {destination?.map((dest, i) => (
                dest?.noOfNight > 0 && (
                  <span key={i} className="inline-flex items-center text-nowrap">
                    <span className="text-[#FF7865] font-Poppins text-[14px] not-italic font-semibold">
                      {dest.noOfNight}N
                    </span>
                    <span className="text-[#6A778B] font-Poppins text-[14px] not-italic font-semibold">
                      &nbsp;-&nbsp;{dest.destinationName}
                    </span>
                    {i < destination.length - 1 && (
                      <span className="text-[#6A778B] mx-2"></span>
                    )}
                  </span>
                )
              ))}
          </span>
        </div>
        <div className="flex justify-between text-sm text-gray-600 mt-2">
          <div className="flex items-center">
            <Clock size={16} className="mr-2 text-green-500" />
            <span>
              {noOfNight} Nights, {noOfDays} Days
            </span>
          </div>
          <h1 className="text-[12px] bg-green-100 rounded-lg px-2 py-1 flex flex-nowrap flex-row whitespace-nowrap items-center leading-none  ">
            {" "}
            Starts <span className="text-[12px] pl-1 mb-[3px]"> @</span>
            <span className="text-[#1EC089] text-[12px]">
              {props.package.startFrom}
            </span>{" "}
          </h1>
        </div>
        <div className="border-t border-gray-200 my-4"></div>
        <div className="text-sm text-gray-800">
          <p className="font-semibold mb-2">What's Included:</p>
          <ul className="space-y-2">
            <li className="flex items-center">
              <Plane size={16} className="mr-2 text-green-500" />
              <span>
                <span className="font-medium">Airport Transfers Included</span> 
              </span>
            </li>
            <li className="flex items-center">
              <Building size={16} className="mr-2 text-green-500" />
              <span>
                <span className="font-medium">{hotelCount} Hotels Included</span>
              </span>
            </li>
            <li className="flex items-center">
              <Utensils size={16} className="mr-2 text-green-500" />
              <span>
                <span className="font-medium">Meals:</span> {mealPlanDisplay}
              </span>
            </li>
            <li className="flex items-center">
              <CarFront size={16} className="mr-2 text-green-500" />
              <span>
                <span className="font-medium">
                  {vehicle && vehicle.length > 0
                    ? `Cab Included:`
                    : "Cab Included"}
                </span>{" "}
                {vehicle?.map((v, i) => (
                  <span key={i}>
                    {v.seater} Seater{i < vehicle.length - 1 ? ", " : ""}
                  </span>
                ))}
              </span>
            </li>
          </ul>
        </div>

        <div className="flex-grow"></div>

        <div className="flex justify-between items-center mt-2 pt-4 border-t border-gray-200">
          <div>
            <p className="text-xl font-bold flex items-center text-app-primary">
              <IndianRupee size={18} className="mr-1" />
              {formatIndianNumber(perPerson)}
            </p>
            <p className="text-xs pl-5 text-gray-500">per person</p>
          </div>
          <button
            onClick={() => router.push(`/package/${_id}`)}
            className="bg-app-secondary text-white font-bold px-3 py-1.5 text-sm rounded-lg hover:bg-emerald-600 transition-colors duration-300 shadow-md hover:shadow-lg"
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterCardList;
