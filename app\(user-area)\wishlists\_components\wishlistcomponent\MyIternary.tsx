import React from "react";
import { MapPin } from "lucide-react";
import { FaLocationDot, FaUser } from "react-icons/fa6";
import Image from "next/image";

const MyIternary = () => {
  const iternaryDetail = [
    {
      label: "Enchanting Manali",
      location: "shimla, Manali",
      Person: "2+1",
    },
    {
      label: "Enchanting Ooty",
      location: "shimla, Manali",
      Person: "4+1",
    },
    {
      label: "Enchanting Kashmirsd",
      location: "shimla, Manali",
      Person: "4+1",
    },
    {
      label: "Enchanting Shimla",
      location: "shimla, Manali",
      Person: "4+1",
    },
  ];
  return (
    <div className="grid grid-cols-2 gap-8">
      {iternaryDetail.map((data, index) => (
        <div
          key={index}
          className="relative mt-[6]  mx-[10px] w-[141px] h-[156px] rounded-[14px] border-2 border-[#FF5F5F] bg-myIternaryContBg shadow-myIternaryContShadow p-[4px]"
        >
          <div className="w-[129px] h-[85px] rounded-lg bg-myIternaryImgContBg"></div>
          <div className=" ml-[3px] mt-[5px]">
            <p
              className=" w-full h-[15px] text-[10px] font-semibold leading-normal tracking-[0.1px] bg-clip-text text-transparent "
              style={{
                backgroundImage:
                  "linear-gradient(87deg, #FF5F5F -25.84%, #FF9080 118.31%)",
              }}
            >
              {data.label}
            </p>
            <div className="mt-[5px] flex ">
              <span className="flex items-center">
                {" "}
                <FaLocationDot
                  size={6}
                  className=" mr-[3px] h-[7px] w-[7px] text-[#41D6A3]"
                />{" "}
                <p
                  className="h-[9px] w-[58.5px] text-[7px] font-medium leading-normal tracking-[0.07px] bg-clip-text text-transparent "
                  style={{
                    backgroundImage:
                      "linear-gradient(90deg, #27B182 -5.26%, #41D6A3 99.73%)",
                    textShadow: "2px 4px 14.3px rgba(30, 192, 137, 0.17)",
                  }}
                >
                  {data.location}
                </p>
              </span>
              <span className="ml-[7px] flex items-center">
                <FaUser
                  size={6}
                  className=" mr-[3px] h-[7px] w-[7px] text-[#FF5F5F]"
                />{" "}
                <p className=" text-[#6A778B] text-[7px] font-medium leading-normal tracking-[0.07px] ">
                  {data.Person}
                </p>
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MyIternary;
