"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { signOut } from "next-auth/react";
import Image from "next/image";
import {
  Headphones,
  Info,
  LogOut,
  TicketCheck,
  UserRound,
  User,
} from "lucide-react";
import toast from "react-hot-toast";
import { useAuth } from "@/app/hooks/useAuth";
import { cleanToken, ClearToken } from "@/app/utils/constants/accessToken";
import { useDispatch } from "react-redux";
import { removePackageId } from "@/app/store/features/packageDetailsSlice";
import TermsAndConditions from "./components/terms-conditions";
import Help from "./components/help";
import Link from "next/link";

const Account = () => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuth();
  const dispatch = useDispatch();

  useEffect(() => {
    console.log("accountPage:", isAuthenticated, isLoading);
    if (!isAuthenticated && !isLoading) router.push("/sign-in");
  }, [isLoading, isAuthenticated, router]);

  const handleLogout = async () => {
    ClearToken();
    localStorage.clear();
    sessionStorage.clear();
    dispatch(removePackageId());
    toast.success("Logged out");
    await signOut({ redirect: true, callbackUrl: "/" });
  };

  const handleBooking = () => {
    router.push("/mybookings");
  };

  return (
    <div className="sm:px-5 sm:pt-6 lg:pt-24 lg:px-10">
      {/* Profile Section */}
      <div className="flex items-center space-x-5 mb-8">
        <div className="relative sm:w-12 sm:h-12 lg:h-[120px] lg:w-[120px] rounded-full">
          {user?.profileImg ? (
            <Image
              unoptimized
              src={user.profileImg || "/default-profile.jpg"}
              className="object-cover rounded-full"
              fill
              alt="Profile"
            />
          ) : (
            <div className="relative w-12 h-12 flex items-center justify-center lg:h-[120px] lg:w-[120px] rounded-full bg-gray-200">
              <UserRound className="text-slate-400" size={30} />
            </div>
          )}
        </div>
        <div className="flex flex-col">
          <h1 className="text-xl lg:text-3xl text-[#FF5F5F] font-semibold">
            {user?.fullName}
          </h1>
          <h1 className="text-sm lg:text-lg text-muted-foreground">
            {user?.mobileNo}
          </h1>
        </div>
      </div>

      {/* Main Content - Flex container for large screens */}
      <div className="lg:flex lg:space-x-8 lg:items-start">
        {/* Account Section */}
        <div className="flex-1 mb-8 lg:mb-0">
          <h1 className="text-[#FF9080] text-xs lg:text-lg">Account</h1>
          <div className="flex flex-col mt-2 space-y-4">
            <Link
              href={`/account/${user?.userId}`}
              className="w-full cursor-pointer h-16 border rounded-lg flex space-x-6 items-center p-2 hover:bg-gray-50"
            >
              <User className="text-[#ff9080]" />
              <h1 className="text-slate-500">My Profile</h1>
            </Link>
            <div
              onClick={handleBooking}
              className="w-full cursor-pointer h-16 border rounded-lg flex space-x-6 items-center p-2 hover:bg-gray-50"
            >
              <TicketCheck className="text-[#ff9080]" />
              <h1 className="text-slate-500">My Bookings</h1>
            </div>
          </div>
        </div>

        {/* Help and Support Section */}
        <div className="flex-1">
          <h1 className="text-[#FF9080] text-xs lg:text-lg">
            Help and Support
          </h1>
          <div className="flex flex-col mt-2 space-y-4">
            <TermsAndConditions />
            <Help />
            <div
              onClick={handleLogout}
              className="cursor-pointer w-full h-16 border rounded-lg flex space-x-6 items-center p-2 hover:bg-gray-50"
            >
              <LogOut className="text-[#ff9080]" />
              <h1 className="text-slate-500">Logout</h1>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Account;
